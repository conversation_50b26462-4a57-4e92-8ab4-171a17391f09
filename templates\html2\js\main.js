// ===== OPTIMIZED MAIN JAVASCRIPT FILE =====

// Performance monitoring
const perfStart = performance.now();

// Global error handler
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
});

// Check if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}

// Main initialization function
function initializeApp() {
    console.log('🚀 Initializing WIDDX App...');

    // Initialize critical functionality immediately
    ensureContentVisibility();
    initPreloader();
    initNavigation();
    initThemeToggle();

    // Initialize language system integration
    initLanguageIntegration();

    // Initialize modern background if available
    if (typeof initModernBackground === 'function') {
        initModernBackground();
        console.log('✅ Modern background system initialized');
    }

    // Performance optimization: Use requestIdleCallback for non-critical tasks
    if ('requestIdleCallback' in window) {
        requestIdleCallback(initNonCriticalFeatures, { timeout: 2000 });
    } else {
        setTimeout(initNonCriticalFeatures, 100);
    }

    // Log performance
    console.log(`✅ Critical JS loaded in ${(performance.now() - perfStart).toFixed(2)}ms`);
}

// Initialize non-critical features
function initNonCriticalFeatures() {
    console.log('🔧 Loading non-critical features...');

    try {
        initScrollToTop();
        initMobileMenu();
        initSmoothScrolling();
        initCounterAnimation();
        initFormValidation();
        initFAQ();
        hideSplineLogo();
        initPerformanceOptimizations();
        ensureFloatingElementsVisible(); // Ensure floating elements are visible

        console.log('✅ All features loaded successfully');
    } catch (error) {
        console.error('❌ Error loading features:', error);
    }
}

// ===== CONTENT VISIBILITY FALLBACK =====
function ensureContentVisibility() {
    console.log('Ensuring content visibility...');

    // Ensure hero content is visible
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        heroContent.style.opacity = '1';
        heroContent.style.visibility = 'visible';
        console.log('Hero content made visible');
    } else {
        console.log('Hero content not found');
    }

    // Ensure all hero elements are visible
    const heroElements = document.querySelectorAll('.title-line, .hero-subtitle, .hero-buttons');
    console.log('Found hero elements:', heroElements.length);
    heroElements.forEach(element => {
        element.style.opacity = '1';
        element.style.visibility = 'visible';
        element.style.transform = 'none';
    });

    // Hide preloader after a maximum time
    setTimeout(() => {
        const preloader = document.getElementById('preloader');
        if (preloader && preloader.style.display !== 'none') {
            preloader.style.display = 'none';
            document.body.style.overflow = 'visible';
            console.log('Preloader hidden by fallback');
        }
    }, 3000);
}

// ===== PRELOADER =====
function initPreloader() {
    const preloader = document.getElementById('preloader');

    // Hide preloader after page load
    window.addEventListener('load', function() {
        setTimeout(() => {
            preloader.style.opacity = '0';
            setTimeout(() => {
                preloader.style.display = 'none';
                document.body.style.overflow = 'visible';
            }, 500);
        }, 1000);
    });
}

// ===== OPTIMIZED NAVIGATION =====
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.nav-link');

    // Throttled navbar scroll effect for better performance
    const handleScroll = throttle(function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }, 16); // ~60fps

    // Optimized active link highlighting
    const updateActiveLink = throttle(function() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === `#${sectionId}` ||
                        (sectionId === 'hero' && link.getAttribute('href') === 'index.html')) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }, 100); // Less frequent updates for active links

    // Use passive listeners for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('scroll', updateActiveLink, { passive: true });
}

// ===== MOBILE MENU =====
function initMobileMenu() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// ===== SCROLL TO TOP BUTTON =====
function initScrollToTop() {
    const scrollToTopBtn = document.getElementById('scrollToTop');

    if (scrollToTopBtn) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }
        });

        // Scroll to top functionality
        scrollToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== COUNTER ANIMATION =====
function initCounterAnimation() {
    const counters = document.querySelectorAll('.stat-number');

    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };

        updateCounter();
    };

    // Intersection Observer for counter animation
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                if (!counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter);
                }
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// ===== FORM VALIDATION =====
function initFormValidation() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const formFields = form.querySelectorAll('input, textarea, select');
            let isValid = true;

            // Clear previous error messages
            form.querySelectorAll('.error-message').forEach(error => {
                error.remove();
            });

            // Validate each field
            formFields.forEach(field => {
                const value = field.value.trim();
                const fieldName = field.getAttribute('name');
                const fieldType = field.getAttribute('type');

                // Remove previous error styling
                field.classList.remove('error');

                // Required field validation
                if (field.hasAttribute('required') && !value) {
                    showFieldError(field, `${getFieldLabel(fieldName)} is required`);
                    isValid = false;
                    return;
                }

                // Email validation
                if (fieldType === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        showFieldError(field, 'Please enter a valid email address');
                        isValid = false;
                        return;
                    }
                }

                // Phone validation
                if (fieldType === 'tel' && value) {
                    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                    if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                        showFieldError(field, 'Please enter a valid phone number');
                        isValid = false;
                        return;
                    }
                }

                // Minimum length validation
                const minLength = field.getAttribute('minlength');
                if (minLength && value.length < parseInt(minLength)) {
                    showFieldError(field, `${getFieldLabel(fieldName)} must be at least ${minLength} characters`);
                    isValid = false;
                    return;
                }
            });

            if (isValid) {
                // Show success message
                showFormSuccess(form, 'Thank you! Your message has been sent successfully.');
                form.reset();
            }
        });
    });
}

// Helper function to show field error
function showFieldError(field, message) {
    field.classList.add('error');

    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    errorElement.style.color = '#ff6b6b';
    errorElement.style.fontSize = '0.875rem';
    errorElement.style.marginTop = '5px';

    field.parentNode.appendChild(errorElement);
}

// Helper function to show form success
function showFormSuccess(form, message) {
    const successElement = document.createElement('div');
    successElement.className = 'success-message';
    successElement.textContent = message;
    successElement.style.color = '#4ecdc4';
    successElement.style.fontSize = '1rem';
    successElement.style.textAlign = 'center';
    successElement.style.padding = '15px';
    successElement.style.background = 'rgba(78, 205, 196, 0.1)';
    successElement.style.borderRadius = '8px';
    successElement.style.marginTop = '20px';

    form.appendChild(successElement);

    // Remove success message after 5 seconds
    setTimeout(() => {
        successElement.remove();
    }, 5000);
}

// Helper function to get field label
function getFieldLabel(fieldName) {
    const labels = {
        'name': 'Name',
        'email': 'Email',
        'phone': 'Phone',
        'subject': 'Subject',
        'message': 'Message',
        'company': 'Company',
        'service': 'Service'
    };

    return labels[fieldName] || fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
}

// ===== UTILITY FUNCTIONS =====

// Debounce function for performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ===== FLOATING ELEMENTS PARALLAX =====
function initParallaxElements() {
    const floatingCards = document.querySelectorAll('.floating-card');

    if (floatingCards.length > 0) {
        console.log('🎈 Found', floatingCards.length, 'floating cards');

        // Ensure floating cards are visible
        floatingCards.forEach((card, index) => {
            card.style.opacity = '1';
            card.style.visibility = 'visible';
            card.style.display = 'flex';
            console.log(`🎈 Floating card ${index + 1}:`, card.textContent.trim());
        });

        window.addEventListener('scroll', throttle(() => {
            const scrolled = window.pageYOffset;

            floatingCards.forEach(card => {
                const speed = card.getAttribute('data-speed') || 1;
                const yPos = -(scrolled * speed * 0.1);
                card.style.transform = `translateY(${yPos}px)`;
            });
        }, 16));
    } else {
        console.warn('⚠️ No floating cards found');
    }
}

// ===== FLOATING ELEMENTS VISIBILITY =====
function ensureFloatingElementsVisible() {
    const floatingElements = document.querySelector('.floating-elements');
    const floatingCards = document.querySelectorAll('.floating-card');

    if (floatingElements) {
        floatingElements.style.opacity = '1';
        floatingElements.style.visibility = 'visible';
        floatingElements.style.zIndex = '10';
        console.log('✅ Floating elements container made visible');
    }

    if (floatingCards.length > 0) {
        floatingCards.forEach((card, index) => {
            card.style.opacity = '1';
            card.style.visibility = 'visible';
            card.style.display = 'flex';
            card.style.position = 'absolute';
            card.style.pointerEvents = 'auto';
            console.log(`✅ Floating card ${index + 1} made visible:`, card.textContent.trim());
        });
    } else {
        console.warn('⚠️ No floating cards found to make visible');
    }
}

// Initialize parallax and visibility on load
window.addEventListener('load', () => {
    ensureFloatingElementsVisible();
    initParallaxElements();
});

// Also ensure visibility on DOM ready
document.addEventListener('DOMContentLoaded', ensureFloatingElementsVisible);

// Debug function to check floating elements status
function debugFloatingElements() {
    console.log('🔍 Debugging floating elements...');

    const floatingElements = document.querySelector('.floating-elements');
    const floatingCards = document.querySelectorAll('.floating-card');
    const heroVisual = document.querySelector('.hero-visual');
    const robotContainer = document.querySelector('.robot-container');

    console.log('Hero visual:', heroVisual ? 'Found' : 'Not found');
    console.log('Robot container:', robotContainer ? 'Found' : 'Not found');
    console.log('Floating elements container:', floatingElements ? 'Found' : 'Not found');
    console.log('Floating cards count:', floatingCards.length);

    if (floatingElements) {
        const styles = getComputedStyle(floatingElements);
        console.log('Floating elements styles:', {
            display: styles.display,
            visibility: styles.visibility,
            opacity: styles.opacity,
            zIndex: styles.zIndex,
            position: styles.position
        });
    }

    floatingCards.forEach((card, index) => {
        const styles = getComputedStyle(card);
        console.log(`Floating card ${index + 1}:`, {
            text: card.textContent.trim(),
            display: styles.display,
            visibility: styles.visibility,
            opacity: styles.opacity,
            position: styles.position,
            top: styles.top,
            left: styles.left,
            right: styles.right,
            bottom: styles.bottom
        });
    });
}

// Add debug function to window for manual testing
window.debugFloatingElements = debugFloatingElements;

// Auto-run debug after page load
window.addEventListener('load', () => {
    setTimeout(debugFloatingElements, 2000);
});

// ===== FAQ FUNCTIONALITY =====
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });

            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');
            }
        });
    });
}

// ===== THEME TOGGLE =====
function initThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    const body = document.body;

    // Check for saved theme preference or default to dark
    const savedTheme = localStorage.getItem('theme') || 'dark';
    console.log('🎨 Initializing theme:', savedTheme);
    setTheme(savedTheme);

    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const currentTheme = getCurrentTheme();
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            console.log('🎨 Switching theme from', currentTheme, 'to', newTheme);
            setTheme(newTheme);

            // Save theme preference
            localStorage.setItem('theme', newTheme);

            // Add animation to the toggle button
            if (typeof gsap !== 'undefined') {
                gsap.to(themeToggle, {
                    duration: 0.3,
                    rotation: 360,
                    ease: 'power2.out'
                });
            }
        });
    } else {
        console.warn('⚠️ Theme toggle button not found');
    }

    function getCurrentTheme() {
        const dataTheme = body.getAttribute('data-theme');
        if (dataTheme === 'light') return 'light';
        if (dataTheme === 'dark') return 'dark';
        // If no data-theme attribute or it's null, check localStorage or default to dark
        return localStorage.getItem('theme') || 'dark';
    }

    function setTheme(theme) {
        // Normalize theme value
        const normalizedTheme = theme === 'light' ? 'light' : 'dark';

        console.log('🎨 Setting theme to:', normalizedTheme);

        // Always set the data-theme attribute explicitly
        body.setAttribute('data-theme', normalizedTheme);

        // Update icon based on theme
        if (themeIcon) {
            if (normalizedTheme === 'light') {
                themeIcon.className = 'fas fa-sun';
                themeIcon.setAttribute('aria-label', 'Switch to dark mode');
            } else {
                themeIcon.className = 'fas fa-moon';
                themeIcon.setAttribute('aria-label', 'Switch to light mode');
            }
        } else {
            console.warn('⚠️ Theme icon not found');
        }

        // Update meta theme-color for mobile browsers
        updateMetaThemeColor(normalizedTheme);

        // Add smooth transition class temporarily
        body.classList.add('theme-transitioning');
        setTimeout(() => {
            body.classList.remove('theme-transitioning');
        }, 300);

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: normalizedTheme }
        }));

        console.log('✅ Theme set successfully to:', normalizedTheme);
    }

    function updateMetaThemeColor(theme) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }

        metaThemeColor.content = theme === 'light' ? '#ffffff' : '#010815';
    }

    // Expose theme functions globally for debugging
    window.setTheme = setTheme;
    window.getCurrentTheme = getCurrentTheme;

    // Ensure theme is applied immediately on page load
    const currentTheme = getCurrentTheme();
    if (currentTheme) {
        document.body.setAttribute('data-theme', currentTheme);
        console.log('🎨 Theme applied on page load:', currentTheme);
    }
}

// ===== LANGUAGE INTEGRATION =====
function initLanguageIntegration() {
    console.log('🌐 Initializing language integration...');

    // Listen for language change events
    window.addEventListener('languageChanged', function(event) {
        const { language, isRTL, translations } = event.detail;
        console.log('🌐 Language changed to:', language, 'RTL:', isRTL);

        // Update GSAP animations for RTL
        updateAnimationsForLanguage(isRTL);

        // Update any dynamic content
        updateDynamicContent(language, translations);

        // Trigger layout recalculation
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 100);
    });

    // Update animations based on language direction
    function updateAnimationsForLanguage(isRTL) {
        if (typeof gsap !== 'undefined') {
            // Update slide animations for RTL
            const slideElements = document.querySelectorAll('.slide-in-left, .slide-in-right');
            slideElements.forEach(element => {
                if (isRTL) {
                    element.classList.toggle('slide-in-left');
                    element.classList.toggle('slide-in-right');
                }
            });
        }
    }

    // Update dynamic content that might not have translation attributes
    function updateDynamicContent(language, translations) {
        // Update any dynamically generated content here
        // This is useful for content that's generated by JavaScript

        // Example: Update floating card text
        const floatingCards = document.querySelectorAll('.floating-card span');
        const cardTranslations = {
            en: ['Hosting', 'Marketing', 'Development', 'Design'],
            ar: ['الاستضافة', 'التسويق', 'التطوير', 'التصميم']
        };

        if (cardTranslations[language]) {
            floatingCards.forEach((card, index) => {
                if (cardTranslations[language][index]) {
                    card.textContent = cardTranslations[language][index];
                }
            });
        }
    }

    console.log('✅ Language integration initialized');
}

// ===== HIDE SPLINE LOGO =====
function hideSplineLogo() {
    // Function to hide the logo
    function hideLogo() {
        // Try multiple selectors to find and hide the Spline logo
        const logoSelectors = [
            '#logo',
            'a[href*="spline.design"]',
            'a[id="logo"]',
            '[href*="spline.design"]'
        ];

        logoSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element && element.style) {
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.opacity = '0';
                    element.style.pointerEvents = 'none';
                }
            });
        });

        // Also try to hide within shadow DOM if accessible
        const splineViewers = document.querySelectorAll('spline-viewer');
        splineViewers.forEach(viewer => {
            if (viewer.shadowRoot) {
                const shadowLogos = viewer.shadowRoot.querySelectorAll('#logo, a[href*="spline.design"]');
                shadowLogos.forEach(logo => {
                    if (logo && logo.style) {
                        logo.style.display = 'none';
                        logo.style.visibility = 'hidden';
                        logo.style.opacity = '0';
                    }
                });
            }
        });
    }

    // Hide logo immediately
    hideLogo();

    // Hide logo after delays to catch dynamically loaded content
    setTimeout(hideLogo, 1000);
    setTimeout(hideLogo, 3000);
    setTimeout(hideLogo, 5000);

    // Set up a mutation observer to catch any new logos that appear
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                hideLogo();
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// ===== PERFORMANCE OPTIMIZATIONS =====
function initPerformanceOptimizations() {
    // Preload critical resources
    preloadCriticalResources();

    // Initialize lazy loading
    initLazyLoading();

    // Optimize images
    optimizeImages();

    // Monitor performance
    monitorPerformance();

    // Reduce layout thrashing
    optimizeLayoutOperations();
}

// Preload critical resources
function preloadCriticalResources() {
    const criticalResources = [
        { href: 'about.html', as: 'document' },
        { href: 'services.html', as: 'document' },
        { href: 'portfolio.html', as: 'document' },
        { href: 'contact.html', as: 'document' }
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = resource.href;
        document.head.appendChild(link);
    });
}

// Initialize lazy loading for images and iframes
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const lazyElements = document.querySelectorAll('[data-src], [data-background]');

        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    if (element.dataset.src) {
                        element.src = element.dataset.src;
                        element.removeAttribute('data-src');
                    }

                    if (element.dataset.background) {
                        element.style.backgroundImage = `url(${element.dataset.background})`;
                        element.removeAttribute('data-background');
                    }

                    element.classList.add('loaded');
                    lazyObserver.unobserve(element);
                }
            });
        }, { rootMargin: '50px' });

        lazyElements.forEach(element => {
            lazyObserver.observe(element);
        });
    }
}

// Optimize images
function optimizeImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        // Add loading="lazy" for native lazy loading
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }

        // Add decoding="async" for better performance
        if (!img.hasAttribute('decoding')) {
            img.setAttribute('decoding', 'async');
        }
    });
}

// Monitor performance
function monitorPerformance() {
    // Core Web Vitals monitoring
    if ('PerformanceObserver' in window) {
        try {
            // Largest Contentful Paint
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                console.log('LCP:', lastEntry.startTime.toFixed(2) + 'ms');
            }).observe({ entryTypes: ['largest-contentful-paint'] });

            // First Input Delay
            new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                entries.forEach(entry => {
                    console.log('FID:', (entry.processingStart - entry.startTime).toFixed(2) + 'ms');
                });
            }).observe({ entryTypes: ['first-input'] });
        } catch (e) {
            console.log('Performance monitoring not fully supported');
        }
    }

    // Page load performance
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                console.log('Performance Metrics:');
                console.log('- Page Load Time:', (perfData.loadEventEnd - perfData.fetchStart).toFixed(2) + 'ms');
                console.log('- DOM Content Loaded:', (perfData.domContentLoadedEventEnd - perfData.fetchStart).toFixed(2) + 'ms');

                const paintEntries = performance.getEntriesByType('paint');
                if (paintEntries.length > 0) {
                    console.log('- First Paint:', paintEntries[0].startTime.toFixed(2) + 'ms');
                }
            }
        }, 100);
    });
}

// Optimize layout operations
function optimizeLayoutOperations() {
    // Batch DOM reads and writes
    let scheduledAnimationFrame = false;
    const domOperations = [];

    window.batchDOMOperation = function(operation) {
        domOperations.push(operation);

        if (!scheduledAnimationFrame) {
            scheduledAnimationFrame = true;
            requestAnimationFrame(() => {
                domOperations.forEach(op => op());
                domOperations.length = 0;
                scheduledAnimationFrame = false;
            });
        }
    };

    // Optimize scroll handlers
    const scrollHandlers = [];
    let ticking = false;

    window.addOptimizedScrollHandler = function(handler) {
        scrollHandlers.push(handler);
    };

    function updateScrollHandlers() {
        scrollHandlers.forEach(handler => handler());
        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateScrollHandlers);
            ticking = true;
        }
    }, { passive: true });
}
// ==
=== INITIALIZATION COMPLETE =====
console.log('🎉 WIDDX App fully initialized');