<?php
/**
 * Lahza Payment Gateway for WHMCS
 * Enhanced Security Implementation
 *
 * @package    WHMCS
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @license    https://www.widdx.com/terms
 * @version    1.1.0
 * @security   Enhanced with input validation, rate limiting, and secure logging
 */

// Prevent direct access
if (!defined("WHMCS")) {
    die("Direct access prohibited");
}

// Security constants
define('LAHZA_MAX_REQUESTS_PER_MINUTE', 60);
define('LAHZA_MAX_REQUESTS_PER_HOUR', 1000);
define('LAHZA_SIGNATURE_ALGORITHM', 'sha256');

// 3D Secure constants
define('LAHZA_3DS_VERSION', '2.2.0');
define('LAHZA_3DS_TIMEOUT', 300); // 5 minutes
define('LAHZA_3DS_CHALLENGE_WINDOW_SIZE', '05'); // Full screen

// Global configuration
$lahza_config = [
    'log_dir' => __DIR__ . '/logs',
    'whmcs_loaded' => false
];

// Initialize WHMCS environment
try {
    $init_path = __DIR__ . '/../../../init.php';
    if (file_exists($init_path)) {
        define('CLIENTAREA', true);
        require_once $init_path;
        require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
        require_once __DIR__ . '/../../../includes/invoicefunctions.php';
        
        // Ensure required WHMCS functions are available
        if (!function_exists('logTransaction') || !function_exists('addInvoicePayment')) {
            throw new Exception('Required WHMCS functions not available');
        }
        
        $lahza_config['whmcs_loaded'] = true;
    }
} catch (Exception $e) {
    // Log error and continue with basic functionality
    @file_put_contents(
        __DIR__ . '/lahza_errors.log',
        date('[Y-m-d H:i:s] ') . 'WHMCS init error: ' . $e->getMessage() . PHP_EOL,
        FILE_APPEND
    );
}

// Ensure log directory exists
if (!file_exists($lahza_config['log_dir'])) {
    @mkdir($lahza_config['log_dir'], 0755, true);
}

// Load enhanced logger and transaction manager
require_once __DIR__ . '/lahza/Logger.php';
require_once __DIR__ . '/lahza/TransactionManager.php';

// Initialize logger instance
$lahza_logger = new LahzaLogger();

/**
 * Security helper functions
 */

/**
 * Validate and sanitize input parameters
 * @param array $params Input parameters
 * @return array Sanitized parameters
 * @throws InvalidArgumentException If validation fails
 */
function lahza_validateInput($params) {
    $required = ['invoiceid', 'amount', 'currency', 'clientdetails'];

    foreach ($required as $field) {
        if (empty($params[$field])) {
            throw new InvalidArgumentException("Missing required field: {$field}");
        }
    }

    // Validate invoice ID
    if (!is_numeric($params['invoiceid']) || $params['invoiceid'] <= 0) {
        throw new InvalidArgumentException("Invalid invoice ID");
    }

    // Validate amount
    if (!is_numeric($params['amount']) || $params['amount'] <= 0) {
        throw new InvalidArgumentException("Invalid amount");
    }

    // Validate currency
    $allowedCurrencies = ['ILS', 'JOD', 'USD'];
    if (!in_array($params['currency'], $allowedCurrencies)) {
        throw new InvalidArgumentException("Unsupported currency: {$params['currency']}");
    }

    // Validate email
    if (empty($params['clientdetails']['email']) || !filter_var($params['clientdetails']['email'], FILTER_VALIDATE_EMAIL)) {
        throw new InvalidArgumentException("Invalid email address");
    }

    return $params;
}

/**
 * Rate limiting check
 * @param string $identifier Unique identifier (IP, user ID, etc.)
 * @return bool True if request is allowed, false if rate limited
 */
function lahza_checkRateLimit($identifier) {
    $cacheFile = __DIR__ . '/logs/rate_limit_' . md5($identifier) . '.json';
    $now = time();

    // Load existing data
    $data = [];
    if (file_exists($cacheFile)) {
        $content = file_get_contents($cacheFile);
        $data = json_decode($content, true) ?: [];
    }

    // Clean old entries (older than 1 hour)
    $data = array_filter($data, function($timestamp) use ($now) {
        return ($now - $timestamp) < 3600;
    });

    // Check limits
    $recentRequests = array_filter($data, function($timestamp) use ($now) {
        return ($now - $timestamp) < 60; // Last minute
    });

    if (count($recentRequests) >= LAHZA_MAX_REQUESTS_PER_MINUTE) {
        return false;
    }

    if (count($data) >= LAHZA_MAX_REQUESTS_PER_HOUR) {
        return false;
    }

    // Add current request
    $data[] = $now;

    // Save updated data
    file_put_contents($cacheFile, json_encode($data));

    return true;
}

/**
 * Enhanced signature verification
 * @param array $data Data to verify
 * @param string $signature Received signature
 * @param string $secret Secret key
 * @return bool True if signature is valid
 */
function lahza_verifySignature($data, $signature, $secret) {
    if (empty($signature) || empty($secret)) {
        return false;
    }

    // Create canonical string representation
    $canonicalString = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

    // Calculate expected signature
    $expectedSignature = hash_hmac(LAHZA_SIGNATURE_ALGORITHM, $canonicalString, $secret);

    // Use timing-safe comparison
    return hash_equals($expectedSignature, $signature);
}

/**
 * Secure logging function using enhanced logger
 * @param string $level Log level (info, warning, error)
 * @param string $message Log message
 * @param array $context Additional context data
 * @param string $category Log category
 */
function lahza_secureLog($level, $message, $context = [], $category = 'general') {
    global $lahza_logger;

    if ($lahza_logger instanceof LahzaLogger) {
        $lahza_logger->log($level, $message, $context, $category);
    } else {
        // Fallback to basic logging if logger not available
        $logFile = __DIR__ . '/logs/lahza_fallback.log';

        // Sanitize sensitive data
        if (isset($context['secretKey'])) {
            $context['secretKey'] = '***REDACTED***';
        }
        if (isset($context['publicKey'])) {
            $context['publicKey'] = substr($context['publicKey'], 0, 8) . '***';
        }

        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'message' => $message,
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        $logLine = json_encode($logEntry, JSON_UNESCAPED_SLASHES) . PHP_EOL;
        @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Standardized error response generator
 * @param string $errorCode Error code for internal tracking
 * @param string $userMessage User-friendly error message
 * @param string $technicalDetails Technical details for logging
 * @param array $context Additional context for logging
 * @return array Standardized error response
 */
function lahza_createErrorResponse($errorCode, $userMessage, $technicalDetails = '', $context = []) {
    // Log the error
    lahza_secureLog('error', "Error {$errorCode}: {$technicalDetails}", array_merge($context, [
        'error_code' => $errorCode,
        'user_message' => $userMessage
    ]));

    // Return standardized error response
    return [
        'status' => 'error',
        'rawdata' => $userMessage,
        'transid' => '',
        'fee' => '0.00',
        'error_code' => $errorCode
    ];
}

/**
 * Standardized success response generator
 * @param string $transactionId Transaction ID
 * @param float $amount Transaction amount
 * @param float $fee Transaction fee
 * @param string $redirectUrl Redirect URL (for third-party gateways)
 * @param array $additionalData Additional response data
 * @return array Standardized success response
 */
function lahza_createSuccessResponse($transactionId, $amount, $fee = 0.00, $redirectUrl = '', $additionalData = []) {
    $response = [
        'status' => 'success',
        'transid' => $transactionId,
        'amount' => $amount,
        'fee' => $fee
    ];

    if (!empty($redirectUrl)) {
        $response['redirect'] = $redirectUrl;
    }

    // Merge any additional data
    $response = array_merge($response, $additionalData);

    // Log the success
    lahza_secureLog('info', 'Payment processed successfully', [
        'transaction_id' => $transactionId,
        'amount' => $amount,
        'fee' => $fee
    ]);

    return $response;
}

/**
 * Enhanced exception handler for payment processing
 * @param Exception $e The exception to handle
 * @param array $context Additional context for logging
 * @return array Error response
 */
function lahza_handleException($e, $context = []) {
    $errorCode = 'LAHZA_' . strtoupper(str_replace(' ', '_', $e->getMessage()));
    $errorCode = preg_replace('/[^A-Z0-9_]/', '', $errorCode);
    $errorCode = substr($errorCode, 0, 50); // Limit length

    // Determine user-friendly message based on exception type
    $userMessage = 'Payment processing failed. Please try again or contact support.';

    if ($e instanceof InvalidArgumentException) {
        $userMessage = 'Invalid payment information provided. Please check your details and try again.';
    } elseif (strpos($e->getMessage(), 'cURL') !== false) {
        $userMessage = 'Connection error. Please check your internet connection and try again.';
    } elseif (strpos($e->getMessage(), 'timeout') !== false) {
        $userMessage = 'Request timeout. Please try again in a few moments.';
    } elseif (strpos($e->getMessage(), 'rate limit') !== false) {
        $userMessage = 'Too many requests. Please wait a moment and try again.';
    }

    return lahza_createErrorResponse(
        $errorCode,
        $userMessage,
        $e->getMessage(),
        array_merge($context, [
            'exception_type' => get_class($e),
            'stack_trace' => $e->getTraceAsString()
        ])
    );
}

/**
 * 3D Secure helper functions
 */

/**
 * Initialize 3D Secure authentication
 * @param array $params Payment parameters
 * @param array $cardData Card information
 * @return array 3DS initialization response
 */
function lahza_init3DSecure($params, $cardData) {
    $threeDSData = [
        'version' => LAHZA_3DS_VERSION,
        'merchant_id' => $params['publicKey'],
        'transaction_id' => $params['invoiceid'] . '_' . time(),
        'amount' => $params['amount'],
        'currency' => $params['currency'],
        'card_number' => $cardData['number'],
        'cardholder_name' => $cardData['name'],
        'browser_info' => lahza_getBrowserInfo(),
        'challenge_window_size' => LAHZA_3DS_CHALLENGE_WINDOW_SIZE,
        'notification_url' => $params['systemurl'] . '/modules/gateways/callback/lahza.php',
        'return_url' => $params['returnurl']
    ];

    lahza_secureLog('info', '3D Secure initialization', [
        'transaction_id' => $threeDSData['transaction_id'],
        'amount' => $params['amount'],
        'currency' => $params['currency']
    ], '3ds');

    return $threeDSData;
}

/**
 * Get browser information for 3D Secure
 * @return array Browser information
 */
function lahza_getBrowserInfo() {
    return [
        'accept_header' => $_SERVER['HTTP_ACCEPT'] ?? '*/*',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
        'language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'en-US',
        'color_depth' => 24, // Default value
        'screen_height' => 1080, // Default value
        'screen_width' => 1920, // Default value
        'timezone' => date('P'),
        'java_enabled' => false,
        'javascript_enabled' => true,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
}

/**
 * Process 3D Secure authentication response
 * @param array $threeDSResponse Response from 3DS provider
 * @return array Processing result
 */
function lahza_process3DSecureResponse($threeDSResponse) {
    $authenticationStatus = $threeDSResponse['authentication_status'] ?? 'N';
    $transactionId = $threeDSResponse['transaction_id'] ?? '';

    lahza_secureLog('info', '3D Secure response received', [
        'transaction_id' => $transactionId,
        'authentication_status' => $authenticationStatus,
        'eci' => $threeDSResponse['eci'] ?? '',
        'cavv' => isset($threeDSResponse['cavv']) ? 'present' : 'absent'
    ], '3ds');

    switch ($authenticationStatus) {
        case 'Y': // Authentication successful
            return [
                'status' => 'authenticated',
                'eci' => $threeDSResponse['eci'],
                'cavv' => $threeDSResponse['cavv'],
                'xid' => $threeDSResponse['xid'] ?? '',
                'message' => '3D Secure authentication successful'
            ];

        case 'A': // Authentication attempted
            return [
                'status' => 'attempted',
                'eci' => $threeDSResponse['eci'],
                'cavv' => $threeDSResponse['cavv'] ?? '',
                'xid' => $threeDSResponse['xid'] ?? '',
                'message' => '3D Secure authentication attempted'
            ];

        case 'N': // Authentication failed
            return [
                'status' => 'failed',
                'message' => '3D Secure authentication failed',
                'error_code' => $threeDSResponse['error_code'] ?? 'AUTH_FAILED'
            ];

        case 'U': // Authentication unavailable
            return [
                'status' => 'unavailable',
                'message' => '3D Secure authentication unavailable',
                'fallback' => true
            ];

        default:
            return [
                'status' => 'error',
                'message' => 'Unknown 3D Secure authentication status',
                'error_code' => 'UNKNOWN_STATUS'
            ];
    }
}

/**
 * Generate 3D Secure challenge form
 * @param array $challengeData Challenge data from provider
 * @return string HTML form for 3DS challenge
 */
function lahza_generate3DSChallengeForm($challengeData) {
    $formHtml = '
    <div id="lahza-3ds-challenge" class="lahza-3ds-container">
        <div class="lahza-3ds-header">
            <h3>Secure Payment Verification</h3>
            <p>Please complete the security verification to proceed with your payment.</p>
        </div>

        <div class="lahza-3ds-frame-container">
            <iframe
                id="lahza-3ds-frame"
                src="' . htmlspecialchars($challengeData['challenge_url']) . '"
                width="100%"
                height="600"
                frameborder="0"
                sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation"
                title="3D Secure Authentication">
            </iframe>
        </div>

        <div class="lahza-3ds-footer">
            <div class="lahza-3ds-security-info">
                <i class="fas fa-shield-alt"></i>
                <span>This verification is provided by your bank for additional security</span>
            </div>
            <div class="lahza-3ds-timeout">
                <span id="lahza-3ds-timer">Time remaining: 5:00</span>
            </div>
        </div>
    </div>

    <script>
        // 3D Secure challenge timeout handler
        let timeLeft = ' . LAHZA_3DS_TIMEOUT . ';
        const timer = setInterval(function() {
            timeLeft--;
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById("lahza-3ds-timer").textContent =
                "Time remaining: " + minutes + ":" + (seconds < 10 ? "0" : "") + seconds;

            if (timeLeft <= 0) {
                clearInterval(timer);
                window.location.href = "' . htmlspecialchars($challengeData['timeout_url']) . '";
            }
        }, 1000);

        // Listen for 3DS completion
        window.addEventListener("message", function(event) {
            if (event.origin === "' . htmlspecialchars($challengeData['origin']) . '") {
                if (event.data.type === "3ds-complete") {
                    clearInterval(timer);
                    window.location.href = event.data.returnUrl;
                }
            }
        });
    </script>

    <style>
        .lahza-3ds-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .lahza-3ds-header {
            background: linear-gradient(135deg, #2c5aa0 0%, #4a90e2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .lahza-3ds-header h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
        }

        .lahza-3ds-header p {
            margin: 0;
            opacity: 0.9;
        }

        .lahza-3ds-frame-container {
            position: relative;
            background: #f8f9fa;
        }

        .lahza-3ds-footer {
            padding: 1rem 2rem;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e9ecef;
        }

        .lahza-3ds-security-info {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 0.875rem;
        }

        .lahza-3ds-security-info i {
            margin-right: 0.5rem;
            color: #28a745;
        }

        .lahza-3ds-timeout {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }
    </style>';

    return $formHtml;
}

/**
 * Define module metadata
 */
function lahza_MetaData() {
    return [
        'DisplayName' => 'Lahza Payment Gateway',
        'APIVersion' => '1.1',
        'RequiresServer' => false,
        'DefaultNonSSLMode' => 'off',
        'DefaultAutoSettle' => true,
        '3DSecure' => true,
        'DisableLocalCreditCardInput' => true,
        'TokenisedStorage' => false,
    ];
}

/**
 * Gateway configuration
 */
function lahza_config() {
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Lahza Payment',
        ],
        'publicKey' => [
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'secretKey' => [
            'FriendlyName' => 'Secret Key',
            'Type' => 'password', 
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'webhookSecret' => [
            'FriendlyName' => 'Webhook Secret',
            'Type' => 'password',
            'Size' => '50',
            'Description' => 'From Lahza webhook settings',
        ],
        'testMode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode',
        ],
        'enable3DS' => [
            'FriendlyName' => '3D Secure Authentication',
            'Type' => 'yesno',
            'Description' => 'Enable 3D Secure authentication for enhanced security (recommended)',
            'Default' => 'on',
        ],
        '3dsTimeout' => [
            'FriendlyName' => '3DS Timeout (seconds)',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '300',
            'Description' => 'Timeout for 3D Secure authentication (default: 300 seconds)',
        ],
    ];
}

/**
 * Process payment with enhanced security
 */
function lahza_capture($params) {
    try {
        // Security: Validate input parameters
        $params = lahza_validateInput($params);

        // Security: Rate limiting check
        $identifier = $_SERVER['REMOTE_ADDR'] . '_' . $params['clientdetails']['userid'];
        if (!lahza_checkRateLimit($identifier)) {
            return lahza_createErrorResponse(
                'RATE_LIMIT_EXCEEDED',
                'Too many requests. Please wait a moment and try again.',
                'Rate limit exceeded for identifier: ' . $identifier,
                ['identifier' => $identifier]
            );
        }

        // Initialize transaction manager
        $transactionManager = new LahzaTransactionManager($params, $lahza_logger);

        // Create transaction record
        $transactionId = $transactionManager->createTransaction(
            $params['invoiceid'],
            $params['amount'],
            $params['currency'],
            [
                'client_id' => $params['clientdetails']['userid'],
                'client_email' => $params['clientdetails']['email'],
                'gateway' => 'lahza',
                'whmcs_version' => $params['whmcsVersion'] ?? 'unknown'
            ]
        );

        // Log payment attempt
        lahza_secureLog('info', 'Payment capture initiated', [
            'invoiceid' => $params['invoiceid'],
            'transaction_id' => $transactionId,
            'amount' => $params['amount'],
            'currency' => $params['currency']
        ]);

        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'transaction_id' => $transactionId,
            'amount' => $params['amount'],
            'description' => 'Processing payment for invoice #' . $params['invoiceid']
        ], 'Attempt');

        // Get gateway parameters
        $publicKey = $params['publicKey'];
        $secretKey = $params['secretKey'];
        $testMode = $params['testMode'] === 'on';

        // Security: Validate API credentials
        if (empty($publicKey) || empty($secretKey)) {
            $transactionManager->updateStatus(
                $transactionId,
                LahzaTransactionManager::STATUS_FAILED,
                'Invalid API credentials'
            );

            return lahza_createErrorResponse(
                'INVALID_CREDENTIALS',
                'Payment gateway configuration error. Please contact support.',
                'Missing API credentials',
                ['invoiceid' => $params['invoiceid'], 'transaction_id' => $transactionId]
            );
        }

        // Set API endpoint
        $apiUrl = 'https://api.lahza.io/transaction/initialize';
            
        // Check if 3D Secure is enabled
        $enable3DS = $params['enable3DS'] === 'on';

        // Prepare payment data according to Lahza API
        $paymentData = [
            'amount' => (int)($params['amount'] * 100), // Convert to smallest currency unit
            'email' => $params['clientdetails']['email'],
            'currency' => strtoupper($params['currency']),
            'reference' => 'INV-' . $params['invoiceid'],
            'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
            'redirect_url' => $params['returnurl'],
            'metadata' => json_encode([
                'invoice_id' => $params['invoiceid'],
                'client_id' => $params['clientdetails']['userid'],
                '3ds_enabled' => $enable3DS,
                'integration' => 'whmcs_widdx_v1.1.0'
            ]),
            'first_name' => $params['clientdetails']['firstname'],
            'last_name' => $params['clientdetails']['lastname']
        ];

        // Add 3D Secure parameters if enabled
        if ($enable3DS) {
            $paymentData['three_d_secure'] = [
                'enabled' => true,
                'version' => LAHZA_3DS_VERSION,
                'challenge_window_size' => LAHZA_3DS_CHALLENGE_WINDOW_SIZE,
                'browser_info' => lahza_getBrowserInfo(),
                'notification_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza_3ds.php'
            ];

            lahza_secureLog('info', '3D Secure enabled for payment', [
                'invoice_id' => $params['invoiceid'],
                '3ds_version' => LAHZA_3DS_VERSION
            ], '3ds');
        }
        
        // Make API request
        $ch = curl_init($apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $publicKey,
                'X-Signature: ' . hash_hmac('sha256', json_encode($paymentData), $secretKey)
            ],
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($paymentData),
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        // Update transaction status to processing
        $transactionManager->updateStatus(
            $transactionId,
            LahzaTransactionManager::STATUS_PROCESSING,
            'API request sent to Lahza'
        );

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            $transactionManager->updateStatus(
                $transactionId,
                LahzaTransactionManager::STATUS_FAILED,
                'cURL Error: ' . $curlError
            );
            throw new Exception("cURL Error: " . $curlError);
        }

        if ($httpCode !== 200) {
            $error = json_decode($response, true);
            $transactionManager->updateStatus(
                $transactionId,
                LahzaTransactionManager::STATUS_FAILED,
                'API Error: HTTP ' . $httpCode . ' - ' . ($error['message'] ?? 'Unknown error')
            );
            throw new Exception($error['message'] ?? "Payment processing failed with HTTP $httpCode");
        }

        $result = json_decode($response, true);

        // Check if 3D Secure is required
        if (isset($result['three_d_secure_required']) && $result['three_d_secure_required']) {
            $transactionManager->updateStatus(
                $transactionId,
                LahzaTransactionManager::TDS_STATUS_REQUIRED,
                '3D Secure authentication required',
                [
                    'gateway_transaction_id' => $result['id'] ?? '',
                    'challenge_url' => $result['three_d_secure_url'] ?? ''
                ]
            );

            return [
                'status' => '3ds_required',
                'redirect' => $result['three_d_secure_url']
            ];
        }

        // Update transaction status to authorized
        $transactionManager->updateStatus(
            $transactionId,
            LahzaTransactionManager::STATUS_AUTHORIZED,
            'Payment authorized by Lahza',
            [
                'gateway_transaction_id' => $result['id'] ?? '',
                'authorization_url' => $result['authorization_url'] ?? ''
            ]
        );

        // Log successful authorization
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'transaction_id' => $transactionId,
            'gateway_transid' => $result['id'],
            'amount' => $params['amount'],
            'status' => 'Authorized'
        ], 'Success');

        // Redirect to payment page
        return [
            'status' => 'success',
            'redirect' => $result['authorization_url']
        ];
        
    } catch (Exception $e) {
        // Update transaction status to failed if transaction was created
        if (isset($transactionManager) && isset($transactionId)) {
            $transactionManager->updateStatus(
                $transactionId,
                LahzaTransactionManager::STATUS_FAILED,
                'Exception: ' . $e->getMessage()
            );
        }

        // Log error using WHMCS transaction log
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'transaction_id' => $transactionId ?? 'unknown',
            'amount' => $params['amount'],
            'error' => $e->getMessage()
        ], 'Failed');

        // Return standardized error response
        return lahza_handleException($e, [
            'invoiceid' => $params['invoiceid'],
            'transaction_id' => $transactionId ?? 'unknown',
            'amount' => $params['amount'],
            'function' => 'lahza_capture'
        ]);
    }
}

/**
 * Generate payment link
 */
function lahza_link($params) {
    // Validate currency
    $allowedCurrencies = ['ILS', 'JOD', 'USD'];
    if (!in_array($params['currency'], $allowedCurrencies)) {
        return "Currency not supported. Please use ILS, JOD or USD.";
    }

    // Prepare API request with correct endpoints and format
    // Lahza uses the same base URL for both test and live; test keys determine sandbox mode
    $apiUrl = 'https://api.lahza.io/transaction/initialize';

    $reference = 'INV-' . $params['invoiceid'] . '-' . time();
    $amountInSmallestUnit = $params['currency'] === 'USD' 
        ? (int)($params['amount'] * 100)  // Convert to cents for USD
        : (int)$params['amount'];         // For ILS and JOD, amount is already in the smallest unit
    
    // Prepare customer information
    $customerEmail = $params['clientdetails']['email'] ?? '';
    $customerName = trim(($params['clientdetails']['firstname'] ?? '') . ' ' . ($params['clientdetails']['lastname'] ?? ''));
    
    $postData = [
        'amount' => $amountInSmallestUnit,
        'email' => $customerEmail,
        'currency' => $params['currency'],
        'reference' => $reference,
        'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
        'redirect_url' => $params['returnurl'],
        'metadata' => json_encode([
            'invoice_id' => $params['invoiceid'],
            'customer_name' => $customerName,
            'whmcs_version' => $params['whmcsVersion']
        ]),
        'first_name' => $params['clientdetails']['firstname'] ?? '',
        'last_name' => $params['clientdetails']['lastname'] ?? '',
    ];

    // Initialize cURL
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $params['secretKey'],
            'Content-Type: application/json',
        ],
        CURLOPT_POSTFIELDS => json_encode($postData),
        CURLOPT_TIMEOUT => 30,
    ]);

    // Execute and handle response
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => 'CreatePayment',
            'error' => $error,
            'data' => $postData,
            'params' => ['invoiceid' => $params['invoiceid']]
        ];
        
        // Log to file
        @file_put_contents(
            __DIR__ . '/logs/lahza_errors.log',
            json_encode($logData) . PHP_EOL,
            FILE_APPEND
        );
        
        // Log to WHMCS if available
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $error,
                'action' => 'Payment Error'
            ], 'Error');
        }
        
        return "Payment gateway error. Please try again later.";
    }

    $responseData = json_decode($response, true);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the request and response for debugging
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'CreatePayment',
        'http_code' => $httpCode,
        'request' => [
            'url' => $apiUrl,
            'data' => $postData
        ],
        'response' => $responseData ?: $response,
        'error' => $error,
        'params' => ['invoiceid' => $params['invoiceid']]
    ];
    
    // Log to file
    @file_put_contents(
        __DIR__ . '/logs/lahza_responses.log',
        json_encode($logData, JSON_PRETTY_PRINT) . PHP_EOL,
        FILE_APPEND
    );
    
    // Handle cURL errors
    if ($error) {
        $errorMsg = 'cURL Error: ' . $error;
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'action' => 'cURL Error'
            ], 'Failed');
        }
        return 'Error connecting to payment gateway: ' . $errorMsg;
    }
    
    // Check if response is valid JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        $errorMsg = 'Invalid JSON response from API: ' . json_last_error_msg();
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $response,
                'action' => 'Invalid JSON Response'
            ], 'Failed');
        }
        return 'Error processing payment response. Please try again.';
    }
    
    // Check API response status
    if (!in_array($httpCode, [200, 201]) || !isset($responseData['status']) || !$responseData['status']) {
        $errorMsg = $responseData['message'] ?? 'Unknown error from payment gateway';
        
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'http_code' => $httpCode,
                'response' => $responseData,
                'action' => 'API Error'
            ], 'Failed');
        }
        
        return 'Payment gateway error: ' . $errorMsg;
    }
    
    // Check if we have a valid authorization URL
    if (empty($responseData['data']['authorization_url'])) {
        $errorMsg = 'Missing authorization URL in response';
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $responseData,
                'action' => 'Invalid Response'
            ], 'Failed');
        }
        return 'Error: Could not process payment. Please contact support.';
    }
    
    // Log successful payment initialization
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'reference' => $reference,
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'action' => 'Payment Initialized',
            'redirect_url' => $responseData['data']['authorization_url']
        ], 'Pending');
    }
    
    // Redirect to payment page
    return '<a href="' . htmlspecialchars($responseData['data']['authorization_url'], ENT_QUOTES) . '" class="btn btn-primary">Pay with Lahza</a>';
}

/**
 * Refund transaction
 * WHMCS calls this when a refund is initiated from the admin area.
 * @param array $params Gateway parameters
 * @return array
 */
function lahza_refund($params)
{
    // Required params
    $secretKey   = $params['secretKey'];
    $transaction = $params['transid'];
    $amount      = $params['amount']; // Decimal
    $currency    = strtoupper($params['currency']);

    // Build API endpoint – assuming Lahza uses /transaction/refund
    $apiUrl = 'https://api.lahza.io/transaction/refund';

    $postData = [
        'transaction_id' => $transaction,
        'amount'         => (int)round($amount * 100), // smallest unit
        'currency'       => $currency,
        'reason'         => 'WHMCS refund request',
    ];

    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST           => true,
        CURLOPT_HTTPHEADER     => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $secretKey,
        ],
        CURLOPT_POSTFIELDS     => json_encode($postData),
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error    = curl_error($ch);
    curl_close($ch);

    // Log interaction
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'action'    => 'Refund',
            'request'   => $postData,
            'response'  => $response,
            'http_code' => $httpCode,
            'error'     => $error,
        ], $error ? 'Error' : 'Info');
    }

    if ($error) {
        return [
            'status'  => 'error',
            'rawdata' => $error,
            'transid' => $transaction,
        ];
    }

    $respData = json_decode($response, true);
    if ($httpCode == 200 && isset($respData['status']) && $respData['status']) {
        return [
            'status'  => 'success',
            'rawdata' => $respData,
            'transid' => $transaction . '-refund',
            'fees'    => 0,
        ];
    }

    return [
        'status'  => 'error',
        'rawdata' => $response,
        'transid' => $transaction,
    ];
}