<?php
/**
 * Lahza Payment Gateway for WHMCS
 *
 * @package    WHMCS
 * <AUTHOR> Company
 * @copyright  Copyright (c) 2025 Your Company
 * @license    https://www.yourcompany.com/terms
 * @version    1.0.0
 */

// Prevent direct access
if (!defined("WHMCS")) {
    die("Direct access prohibited");
}

// Global configuration
$lahza_config = [
    'log_dir' => __DIR__ . '/logs',
    'whmcs_loaded' => false
];

// Initialize WHMCS environment
try {
    $init_path = __DIR__ . '/../../../init.php';
    if (file_exists($init_path)) {
        define('CLIENTAREA', true);
        require_once $init_path;
        require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
        require_once __DIR__ . '/../../../includes/invoicefunctions.php';
        
        // Ensure required WHMCS functions are available
        if (!function_exists('logTransaction') || !function_exists('addInvoicePayment')) {
            throw new Exception('Required WHMCS functions not available');
        }
        
        $lahza_config['whmcs_loaded'] = true;
    }
} catch (Exception $e) {
    // Log error and continue with basic functionality
    @file_put_contents(
        __DIR__ . '/lahza_errors.log',
        date('[Y-m-d H:i:s] ') . 'WHMCS init error: ' . $e->getMessage() . PHP_EOL,
        FILE_APPEND
    );
}

// Ensure log directory exists
if (!file_exists($lahza_config['log_dir'])) {
    @mkdir($lahza_config['log_dir'], 0755, true);
}

/**
 * Define module metadata
 */
function lahza_MetaData() {
    return [
        'DisplayName' => 'Lahza Payment Gateway',
        'APIVersion' => '1.1',
        'RequiresServer' => false,
        'DefaultNonSSLMode' => 'off',
        'DefaultAutoSettle' => true,
    ];
}

/**
 * Gateway configuration
 */
function lahza_config() {
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Lahza Payment',
        ],
        'publicKey' => [
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'secretKey' => [
            'FriendlyName' => 'Secret Key',
            'Type' => 'password', 
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'webhookSecret' => [
            'FriendlyName' => 'Webhook Secret',
            'Type' => 'password',
            'Size' => '50',
            'Description' => 'From Lahza webhook settings',
        ],
        'testMode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode',
        ],
    ];
}

/**
 * Process payment
 */
function lahza_capture($params) {
    // Log payment attempt
    logTransaction('lahza', [
        'invoiceid' => $params['invoiceid'],
        'amount' => $params['amount'],
        'description' => 'Processing payment for invoice #' . $params['invoiceid']
    ], 'Attempt');
    
    try {
        // Get gateway parameters
        $publicKey = $params['publicKey'];
        $secretKey = $params['secretKey'];
        $testMode = $params['testMode'] === 'on';
        
        // Set API endpoint
        $apiUrl = 'https://api.lahza.io/transaction/initialize';
            
        // Prepare payment data according to Lahza API
        $paymentData = [
            'amount' => (int)($params['amount'] * 100), // Convert to smallest currency unit
            'email' => $params['clientdetails']['email'],
            'currency' => strtoupper($params['currency']),
            'reference' => 'INV-' . $params['invoiceid'],
            'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
        'redirect_url' => $params['returnurl'],
            'metadata' => json_encode([
                'invoice_id' => $params['invoiceid'],
                'client_id' => $params['clientdetails']['userid']
            ]),
            'first_name' => $params['clientdetails']['firstname'],
            'last_name' => $params['clientdetails']['lastname']
        ];
        
        // Make API request
        $ch = curl_init($apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $publicKey,
                'X-Signature: ' . hash_hmac('sha256', json_encode($paymentData), $secretKey)
            ],
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($paymentData),
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError) {
            throw new Exception("cURL Error: " . $curlError);
        }
        
        if ($httpCode !== 200) {
            $error = json_decode($response, true);
            throw new Exception($error['message'] ?? "Payment processing failed with HTTP $httpCode");
        }
        
        $result = json_decode($response, true);
        
        // Log successful payment
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'transid' => $result['id'],
            'amount' => $params['amount'],
            'fee' => $result['fee'] ?? '0.00',
            'status' => 'Success'
        ], 'Success');
        
        // Update invoice status
        addInvoicePayment(
            $params['invoiceid'],
            $result['id'],
            $params['amount'],
            $result['fee'] ?? '0.00',
            'lahza'
        );
        
        // Redirect to payment page
        return [
            'status' => 'success',
            'redirect' => $result['authorization_url']
        ];
        
    } catch (Exception $e) {
        // Log error
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'amount' => $params['amount'],
            'error' => $e->getMessage()
        ], 'Failed');
        
        return [
            'status' => 'error',
            'rawdata' => $e->getMessage(),
            'transid' => '',
            'fee' => '0.00'
        ];
    }
}

/**
 * Generate payment link
 */
function lahza_link($params) {
    // Validate currency
    $allowedCurrencies = ['ILS', 'JOD', 'USD'];
    if (!in_array($params['currency'], $allowedCurrencies)) {
        return "Currency not supported. Please use ILS, JOD or USD.";
    }

    // Prepare API request with correct endpoints and format
    // Lahza uses the same base URL for both test and live; test keys determine sandbox mode
    $apiUrl = 'https://api.lahza.io/transaction/initialize';

    $reference = 'INV-' . $params['invoiceid'] . '-' . time();
    $amountInSmallestUnit = $params['currency'] === 'USD' 
        ? (int)($params['amount'] * 100)  // Convert to cents for USD
        : (int)$params['amount'];         // For ILS and JOD, amount is already in the smallest unit
    
    // Prepare customer information
    $customerEmail = $params['clientdetails']['email'] ?? '';
    $customerName = trim(($params['clientdetails']['firstname'] ?? '') . ' ' . ($params['clientdetails']['lastname'] ?? ''));
    
    $postData = [
        'amount' => $amountInSmallestUnit,
        'email' => $customerEmail,
        'currency' => $params['currency'],
        'reference' => $reference,
        'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
        'redirect_url' => $params['returnurl'],
        'metadata' => json_encode([
            'invoice_id' => $params['invoiceid'],
            'customer_name' => $customerName,
            'whmcs_version' => $params['whmcsVersion']
        ]),
        'first_name' => $params['clientdetails']['firstname'] ?? '',
        'last_name' => $params['clientdetails']['lastname'] ?? '',
    ];

    // Initialize cURL
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $params['secretKey'],
            'Content-Type: application/json',
        ],
        CURLOPT_POSTFIELDS => json_encode($postData),
        CURLOPT_TIMEOUT => 30,
    ]);

    // Execute and handle response
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => 'CreatePayment',
            'error' => $error,
            'data' => $postData,
            'params' => ['invoiceid' => $params['invoiceid']]
        ];
        
        // Log to file
        @file_put_contents(
            __DIR__ . '/logs/lahza_errors.log',
            json_encode($logData) . PHP_EOL,
            FILE_APPEND
        );
        
        // Log to WHMCS if available
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $error,
                'action' => 'Payment Error'
            ], 'Error');
        }
        
        return "Payment gateway error. Please try again later.";
    }

    $responseData = json_decode($response, true);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the request and response for debugging
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'CreatePayment',
        'http_code' => $httpCode,
        'request' => [
            'url' => $apiUrl,
            'data' => $postData
        ],
        'response' => $responseData ?: $response,
        'error' => $error,
        'params' => ['invoiceid' => $params['invoiceid']]
    ];
    
    // Log to file
    @file_put_contents(
        __DIR__ . '/logs/lahza_responses.log',
        json_encode($logData, JSON_PRETTY_PRINT) . PHP_EOL,
        FILE_APPEND
    );
    
    // Handle cURL errors
    if ($error) {
        $errorMsg = 'cURL Error: ' . $error;
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'action' => 'cURL Error'
            ], 'Failed');
        }
        return 'Error connecting to payment gateway: ' . $errorMsg;
    }
    
    // Check if response is valid JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        $errorMsg = 'Invalid JSON response from API: ' . json_last_error_msg();
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $response,
                'action' => 'Invalid JSON Response'
            ], 'Failed');
        }
        return 'Error processing payment response. Please try again.';
    }
    
    // Check API response status
    if (!in_array($httpCode, [200, 201]) || !isset($responseData['status']) || !$responseData['status']) {
        $errorMsg = $responseData['message'] ?? 'Unknown error from payment gateway';
        
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'http_code' => $httpCode,
                'response' => $responseData,
                'action' => 'API Error'
            ], 'Failed');
        }
        
        return 'Payment gateway error: ' . $errorMsg;
    }
    
    // Check if we have a valid authorization URL
    if (empty($responseData['data']['authorization_url'])) {
        $errorMsg = 'Missing authorization URL in response';
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $responseData,
                'action' => 'Invalid Response'
            ], 'Failed');
        }
        return 'Error: Could not process payment. Please contact support.';
    }
    
    // Log successful payment initialization
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'reference' => $reference,
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'action' => 'Payment Initialized',
            'redirect_url' => $responseData['data']['authorization_url']
        ], 'Pending');
    }
    
    // Redirect to payment page
    return '<a href="' . htmlspecialchars($responseData['data']['authorization_url'], ENT_QUOTES) . '" class="btn btn-primary">Pay with Lahza</a>';
}

/**
 * Refund transaction
 * WHMCS calls this when a refund is initiated from the admin area.
 * @param array $params Gateway parameters
 * @return array
 */
function lahza_refund($params)
{
    // Required params
    $secretKey   = $params['secretKey'];
    $transaction = $params['transid'];
    $amount      = $params['amount']; // Decimal
    $currency    = strtoupper($params['currency']);

    // Build API endpoint – assuming Lahza uses /transaction/refund
    $apiUrl = 'https://api.lahza.io/transaction/refund';

    $postData = [
        'transaction_id' => $transaction,
        'amount'         => (int)round($amount * 100), // smallest unit
        'currency'       => $currency,
        'reason'         => 'WHMCS refund request',
    ];

    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST           => true,
        CURLOPT_HTTPHEADER     => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $secretKey,
        ],
        CURLOPT_POSTFIELDS     => json_encode($postData),
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error    = curl_error($ch);
    curl_close($ch);

    // Log interaction
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'action'    => 'Refund',
            'request'   => $postData,
            'response'  => $response,
            'http_code' => $httpCode,
            'error'     => $error,
        ], $error ? 'Error' : 'Info');
    }

    if ($error) {
        return [
            'status'  => 'error',
            'rawdata' => $error,
            'transid' => $transaction,
        ];
    }

    $respData = json_decode($response, true);
    if ($httpCode == 200 && isset($respData['status']) && $respData['status']) {
        return [
            'status'  => 'success',
            'rawdata' => $respData,
            'transid' => $transaction . '-refund',
            'fees'    => 0,
        ];
    }

    return [
        'status'  => 'error',
        'rawdata' => $response,
        'transid' => $transaction,
    ];
}