<?php
/**
 * Lahza Payment Gateway for WHMCS
 * Enhanced Security Implementation
 *
 * @package    WHMCS
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 WIDDX
 * @license    https://www.widdx.com/terms
 * @version    1.1.0
 * @security   Enhanced with input validation, rate limiting, and secure logging
 */

// Prevent direct access
if (!defined("WHMCS")) {
    die("Direct access prohibited");
}

// Security constants
define('LAHZA_MAX_REQUESTS_PER_MINUTE', 60);
define('LAHZA_MAX_REQUESTS_PER_HOUR', 1000);
define('LAHZA_SIGNATURE_ALGORITHM', 'sha256');

// Global configuration
$lahza_config = [
    'log_dir' => __DIR__ . '/logs',
    'whmcs_loaded' => false
];

// Initialize WHMCS environment
try {
    $init_path = __DIR__ . '/../../../init.php';
    if (file_exists($init_path)) {
        define('CLIENTAREA', true);
        require_once $init_path;
        require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
        require_once __DIR__ . '/../../../includes/invoicefunctions.php';
        
        // Ensure required WHMCS functions are available
        if (!function_exists('logTransaction') || !function_exists('addInvoicePayment')) {
            throw new Exception('Required WHMCS functions not available');
        }
        
        $lahza_config['whmcs_loaded'] = true;
    }
} catch (Exception $e) {
    // Log error and continue with basic functionality
    @file_put_contents(
        __DIR__ . '/lahza_errors.log',
        date('[Y-m-d H:i:s] ') . 'WHMCS init error: ' . $e->getMessage() . PHP_EOL,
        FILE_APPEND
    );
}

// Ensure log directory exists
if (!file_exists($lahza_config['log_dir'])) {
    @mkdir($lahza_config['log_dir'], 0755, true);
}

// Load enhanced logger
require_once __DIR__ . '/lahza/Logger.php';

// Initialize logger instance
$lahza_logger = new LahzaLogger();

/**
 * Security helper functions
 */

/**
 * Validate and sanitize input parameters
 * @param array $params Input parameters
 * @return array Sanitized parameters
 * @throws InvalidArgumentException If validation fails
 */
function lahza_validateInput($params) {
    $required = ['invoiceid', 'amount', 'currency', 'clientdetails'];

    foreach ($required as $field) {
        if (empty($params[$field])) {
            throw new InvalidArgumentException("Missing required field: {$field}");
        }
    }

    // Validate invoice ID
    if (!is_numeric($params['invoiceid']) || $params['invoiceid'] <= 0) {
        throw new InvalidArgumentException("Invalid invoice ID");
    }

    // Validate amount
    if (!is_numeric($params['amount']) || $params['amount'] <= 0) {
        throw new InvalidArgumentException("Invalid amount");
    }

    // Validate currency
    $allowedCurrencies = ['ILS', 'JOD', 'USD'];
    if (!in_array($params['currency'], $allowedCurrencies)) {
        throw new InvalidArgumentException("Unsupported currency: {$params['currency']}");
    }

    // Validate email
    if (empty($params['clientdetails']['email']) || !filter_var($params['clientdetails']['email'], FILTER_VALIDATE_EMAIL)) {
        throw new InvalidArgumentException("Invalid email address");
    }

    return $params;
}

/**
 * Rate limiting check
 * @param string $identifier Unique identifier (IP, user ID, etc.)
 * @return bool True if request is allowed, false if rate limited
 */
function lahza_checkRateLimit($identifier) {
    $cacheFile = __DIR__ . '/logs/rate_limit_' . md5($identifier) . '.json';
    $now = time();

    // Load existing data
    $data = [];
    if (file_exists($cacheFile)) {
        $content = file_get_contents($cacheFile);
        $data = json_decode($content, true) ?: [];
    }

    // Clean old entries (older than 1 hour)
    $data = array_filter($data, function($timestamp) use ($now) {
        return ($now - $timestamp) < 3600;
    });

    // Check limits
    $recentRequests = array_filter($data, function($timestamp) use ($now) {
        return ($now - $timestamp) < 60; // Last minute
    });

    if (count($recentRequests) >= LAHZA_MAX_REQUESTS_PER_MINUTE) {
        return false;
    }

    if (count($data) >= LAHZA_MAX_REQUESTS_PER_HOUR) {
        return false;
    }

    // Add current request
    $data[] = $now;

    // Save updated data
    file_put_contents($cacheFile, json_encode($data));

    return true;
}

/**
 * Enhanced signature verification
 * @param array $data Data to verify
 * @param string $signature Received signature
 * @param string $secret Secret key
 * @return bool True if signature is valid
 */
function lahza_verifySignature($data, $signature, $secret) {
    if (empty($signature) || empty($secret)) {
        return false;
    }

    // Create canonical string representation
    $canonicalString = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

    // Calculate expected signature
    $expectedSignature = hash_hmac(LAHZA_SIGNATURE_ALGORITHM, $canonicalString, $secret);

    // Use timing-safe comparison
    return hash_equals($expectedSignature, $signature);
}

/**
 * Secure logging function using enhanced logger
 * @param string $level Log level (info, warning, error)
 * @param string $message Log message
 * @param array $context Additional context data
 * @param string $category Log category
 */
function lahza_secureLog($level, $message, $context = [], $category = 'general') {
    global $lahza_logger;

    if ($lahza_logger instanceof LahzaLogger) {
        $lahza_logger->log($level, $message, $context, $category);
    } else {
        // Fallback to basic logging if logger not available
        $logFile = __DIR__ . '/logs/lahza_fallback.log';

        // Sanitize sensitive data
        if (isset($context['secretKey'])) {
            $context['secretKey'] = '***REDACTED***';
        }
        if (isset($context['publicKey'])) {
            $context['publicKey'] = substr($context['publicKey'], 0, 8) . '***';
        }

        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'message' => $message,
            'context' => $context,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];

        $logLine = json_encode($logEntry, JSON_UNESCAPED_SLASHES) . PHP_EOL;
        @file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Standardized error response generator
 * @param string $errorCode Error code for internal tracking
 * @param string $userMessage User-friendly error message
 * @param string $technicalDetails Technical details for logging
 * @param array $context Additional context for logging
 * @return array Standardized error response
 */
function lahza_createErrorResponse($errorCode, $userMessage, $technicalDetails = '', $context = []) {
    // Log the error
    lahza_secureLog('error', "Error {$errorCode}: {$technicalDetails}", array_merge($context, [
        'error_code' => $errorCode,
        'user_message' => $userMessage
    ]));

    // Return standardized error response
    return [
        'status' => 'error',
        'rawdata' => $userMessage,
        'transid' => '',
        'fee' => '0.00',
        'error_code' => $errorCode
    ];
}

/**
 * Standardized success response generator
 * @param string $transactionId Transaction ID
 * @param float $amount Transaction amount
 * @param float $fee Transaction fee
 * @param string $redirectUrl Redirect URL (for third-party gateways)
 * @param array $additionalData Additional response data
 * @return array Standardized success response
 */
function lahza_createSuccessResponse($transactionId, $amount, $fee = 0.00, $redirectUrl = '', $additionalData = []) {
    $response = [
        'status' => 'success',
        'transid' => $transactionId,
        'amount' => $amount,
        'fee' => $fee
    ];

    if (!empty($redirectUrl)) {
        $response['redirect'] = $redirectUrl;
    }

    // Merge any additional data
    $response = array_merge($response, $additionalData);

    // Log the success
    lahza_secureLog('info', 'Payment processed successfully', [
        'transaction_id' => $transactionId,
        'amount' => $amount,
        'fee' => $fee
    ]);

    return $response;
}

/**
 * Enhanced exception handler for payment processing
 * @param Exception $e The exception to handle
 * @param array $context Additional context for logging
 * @return array Error response
 */
function lahza_handleException($e, $context = []) {
    $errorCode = 'LAHZA_' . strtoupper(str_replace(' ', '_', $e->getMessage()));
    $errorCode = preg_replace('/[^A-Z0-9_]/', '', $errorCode);
    $errorCode = substr($errorCode, 0, 50); // Limit length

    // Determine user-friendly message based on exception type
    $userMessage = 'Payment processing failed. Please try again or contact support.';

    if ($e instanceof InvalidArgumentException) {
        $userMessage = 'Invalid payment information provided. Please check your details and try again.';
    } elseif (strpos($e->getMessage(), 'cURL') !== false) {
        $userMessage = 'Connection error. Please check your internet connection and try again.';
    } elseif (strpos($e->getMessage(), 'timeout') !== false) {
        $userMessage = 'Request timeout. Please try again in a few moments.';
    } elseif (strpos($e->getMessage(), 'rate limit') !== false) {
        $userMessage = 'Too many requests. Please wait a moment and try again.';
    }

    return lahza_createErrorResponse(
        $errorCode,
        $userMessage,
        $e->getMessage(),
        array_merge($context, [
            'exception_type' => get_class($e),
            'stack_trace' => $e->getTraceAsString()
        ])
    );
}

/**
 * Define module metadata
 */
function lahza_MetaData() {
    return [
        'DisplayName' => 'Lahza Payment Gateway',
        'APIVersion' => '1.1',
        'RequiresServer' => false,
        'DefaultNonSSLMode' => 'off',
        'DefaultAutoSettle' => true,
    ];
}

/**
 * Gateway configuration
 */
function lahza_config() {
    return [
        'FriendlyName' => [
            'Type' => 'System',
            'Value' => 'Lahza Payment',
        ],
        'publicKey' => [
            'FriendlyName' => 'Public Key',
            'Type' => 'text',
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'secretKey' => [
            'FriendlyName' => 'Secret Key',
            'Type' => 'password', 
            'Size' => '50',
            'Description' => 'From Lahza merchant dashboard',
        ],
        'webhookSecret' => [
            'FriendlyName' => 'Webhook Secret',
            'Type' => 'password',
            'Size' => '50',
            'Description' => 'From Lahza webhook settings',
        ],
        'testMode' => [
            'FriendlyName' => 'Test Mode',
            'Type' => 'yesno',
            'Description' => 'Enable test mode',
        ],
    ];
}

/**
 * Process payment with enhanced security
 */
function lahza_capture($params) {
    try {
        // Security: Validate input parameters
        $params = lahza_validateInput($params);

        // Security: Rate limiting check
        $identifier = $_SERVER['REMOTE_ADDR'] . '_' . $params['clientdetails']['userid'];
        if (!lahza_checkRateLimit($identifier)) {
            return lahza_createErrorResponse(
                'RATE_LIMIT_EXCEEDED',
                'Too many requests. Please wait a moment and try again.',
                'Rate limit exceeded for identifier: ' . $identifier,
                ['identifier' => $identifier]
            );
        }

        // Log payment attempt
        lahza_secureLog('info', 'Payment capture initiated', [
            'invoiceid' => $params['invoiceid'],
            'amount' => $params['amount'],
            'currency' => $params['currency']
        ]);

        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'amount' => $params['amount'],
            'description' => 'Processing payment for invoice #' . $params['invoiceid']
        ], 'Attempt');

        // Get gateway parameters
        $publicKey = $params['publicKey'];
        $secretKey = $params['secretKey'];
        $testMode = $params['testMode'] === 'on';

        // Security: Validate API credentials
        if (empty($publicKey) || empty($secretKey)) {
            return lahza_createErrorResponse(
                'INVALID_CREDENTIALS',
                'Payment gateway configuration error. Please contact support.',
                'Missing API credentials',
                ['invoiceid' => $params['invoiceid']]
            );
        }

        // Set API endpoint
        $apiUrl = 'https://api.lahza.io/transaction/initialize';
            
        // Prepare payment data according to Lahza API
        $paymentData = [
            'amount' => (int)($params['amount'] * 100), // Convert to smallest currency unit
            'email' => $params['clientdetails']['email'],
            'currency' => strtoupper($params['currency']),
            'reference' => 'INV-' . $params['invoiceid'],
            'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
        'redirect_url' => $params['returnurl'],
            'metadata' => json_encode([
                'invoice_id' => $params['invoiceid'],
                'client_id' => $params['clientdetails']['userid']
            ]),
            'first_name' => $params['clientdetails']['firstname'],
            'last_name' => $params['clientdetails']['lastname']
        ];
        
        // Make API request
        $ch = curl_init($apiUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $publicKey,
                'X-Signature: ' . hash_hmac('sha256', json_encode($paymentData), $secretKey)
            ],
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($paymentData),
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError) {
            throw new Exception("cURL Error: " . $curlError);
        }
        
        if ($httpCode !== 200) {
            $error = json_decode($response, true);
            throw new Exception($error['message'] ?? "Payment processing failed with HTTP $httpCode");
        }
        
        $result = json_decode($response, true);
        
        // Log successful payment
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'transid' => $result['id'],
            'amount' => $params['amount'],
            'fee' => $result['fee'] ?? '0.00',
            'status' => 'Success'
        ], 'Success');
        
        // Update invoice status
        addInvoicePayment(
            $params['invoiceid'],
            $result['id'],
            $params['amount'],
            $result['fee'] ?? '0.00',
            'lahza'
        );
        
        // Redirect to payment page
        return [
            'status' => 'success',
            'redirect' => $result['authorization_url']
        ];
        
    } catch (Exception $e) {
        // Log error using WHMCS transaction log
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'amount' => $params['amount'],
            'error' => $e->getMessage()
        ], 'Failed');

        // Return standardized error response
        return lahza_handleException($e, [
            'invoiceid' => $params['invoiceid'],
            'amount' => $params['amount'],
            'function' => 'lahza_capture'
        ]);
    }
}

/**
 * Generate payment link
 */
function lahza_link($params) {
    // Validate currency
    $allowedCurrencies = ['ILS', 'JOD', 'USD'];
    if (!in_array($params['currency'], $allowedCurrencies)) {
        return "Currency not supported. Please use ILS, JOD or USD.";
    }

    // Prepare API request with correct endpoints and format
    // Lahza uses the same base URL for both test and live; test keys determine sandbox mode
    $apiUrl = 'https://api.lahza.io/transaction/initialize';

    $reference = 'INV-' . $params['invoiceid'] . '-' . time();
    $amountInSmallestUnit = $params['currency'] === 'USD' 
        ? (int)($params['amount'] * 100)  // Convert to cents for USD
        : (int)$params['amount'];         // For ILS and JOD, amount is already in the smallest unit
    
    // Prepare customer information
    $customerEmail = $params['clientdetails']['email'] ?? '';
    $customerName = trim(($params['clientdetails']['firstname'] ?? '') . ' ' . ($params['clientdetails']['lastname'] ?? ''));
    
    $postData = [
        'amount' => $amountInSmallestUnit,
        'email' => $customerEmail,
        'currency' => $params['currency'],
        'reference' => $reference,
        'callback_url' => rtrim($params['systemurl'], '/') . '/modules/gateways/callback/lahza.php',
        'redirect_url' => $params['returnurl'],
        'metadata' => json_encode([
            'invoice_id' => $params['invoiceid'],
            'customer_name' => $customerName,
            'whmcs_version' => $params['whmcsVersion']
        ]),
        'first_name' => $params['clientdetails']['firstname'] ?? '',
        'last_name' => $params['clientdetails']['lastname'] ?? '',
    ];

    // Initialize cURL
    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $params['secretKey'],
            'Content-Type: application/json',
        ],
        CURLOPT_POSTFIELDS => json_encode($postData),
        CURLOPT_TIMEOUT => 30,
    ]);

    // Execute and handle response
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => 'CreatePayment',
            'error' => $error,
            'data' => $postData,
            'params' => ['invoiceid' => $params['invoiceid']]
        ];
        
        // Log to file
        @file_put_contents(
            __DIR__ . '/logs/lahza_errors.log',
            json_encode($logData) . PHP_EOL,
            FILE_APPEND
        );
        
        // Log to WHMCS if available
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $error,
                'action' => 'Payment Error'
            ], 'Error');
        }
        
        return "Payment gateway error. Please try again later.";
    }

    $responseData = json_decode($response, true);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Log the request and response for debugging
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'CreatePayment',
        'http_code' => $httpCode,
        'request' => [
            'url' => $apiUrl,
            'data' => $postData
        ],
        'response' => $responseData ?: $response,
        'error' => $error,
        'params' => ['invoiceid' => $params['invoiceid']]
    ];
    
    // Log to file
    @file_put_contents(
        __DIR__ . '/logs/lahza_responses.log',
        json_encode($logData, JSON_PRETTY_PRINT) . PHP_EOL,
        FILE_APPEND
    );
    
    // Handle cURL errors
    if ($error) {
        $errorMsg = 'cURL Error: ' . $error;
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'action' => 'cURL Error'
            ], 'Failed');
        }
        return 'Error connecting to payment gateway: ' . $errorMsg;
    }
    
    // Check if response is valid JSON
    if (json_last_error() !== JSON_ERROR_NONE) {
        $errorMsg = 'Invalid JSON response from API: ' . json_last_error_msg();
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $response,
                'action' => 'Invalid JSON Response'
            ], 'Failed');
        }
        return 'Error processing payment response. Please try again.';
    }
    
    // Check API response status
    if (!in_array($httpCode, [200, 201]) || !isset($responseData['status']) || !$responseData['status']) {
        $errorMsg = $responseData['message'] ?? 'Unknown error from payment gateway';
        
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'http_code' => $httpCode,
                'response' => $responseData,
                'action' => 'API Error'
            ], 'Failed');
        }
        
        return 'Payment gateway error: ' . $errorMsg;
    }
    
    // Check if we have a valid authorization URL
    if (empty($responseData['data']['authorization_url'])) {
        $errorMsg = 'Missing authorization URL in response';
        if (function_exists('logTransaction')) {
            logTransaction('lahza', [
                'invoiceid' => $params['invoiceid'],
                'error' => $errorMsg,
                'response' => $responseData,
                'action' => 'Invalid Response'
            ], 'Failed');
        }
        return 'Error: Could not process payment. Please contact support.';
    }
    
    // Log successful payment initialization
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'invoiceid' => $params['invoiceid'],
            'reference' => $reference,
            'amount' => $params['amount'],
            'currency' => $params['currency'],
            'action' => 'Payment Initialized',
            'redirect_url' => $responseData['data']['authorization_url']
        ], 'Pending');
    }
    
    // Redirect to payment page
    return '<a href="' . htmlspecialchars($responseData['data']['authorization_url'], ENT_QUOTES) . '" class="btn btn-primary">Pay with Lahza</a>';
}

/**
 * Refund transaction
 * WHMCS calls this when a refund is initiated from the admin area.
 * @param array $params Gateway parameters
 * @return array
 */
function lahza_refund($params)
{
    // Required params
    $secretKey   = $params['secretKey'];
    $transaction = $params['transid'];
    $amount      = $params['amount']; // Decimal
    $currency    = strtoupper($params['currency']);

    // Build API endpoint – assuming Lahza uses /transaction/refund
    $apiUrl = 'https://api.lahza.io/transaction/refund';

    $postData = [
        'transaction_id' => $transaction,
        'amount'         => (int)round($amount * 100), // smallest unit
        'currency'       => $currency,
        'reason'         => 'WHMCS refund request',
    ];

    $ch = curl_init($apiUrl);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST           => true,
        CURLOPT_HTTPHEADER     => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $secretKey,
        ],
        CURLOPT_POSTFIELDS     => json_encode($postData),
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error    = curl_error($ch);
    curl_close($ch);

    // Log interaction
    if (function_exists('logTransaction')) {
        logTransaction('lahza', [
            'action'    => 'Refund',
            'request'   => $postData,
            'response'  => $response,
            'http_code' => $httpCode,
            'error'     => $error,
        ], $error ? 'Error' : 'Info');
    }

    if ($error) {
        return [
            'status'  => 'error',
            'rawdata' => $error,
            'transid' => $transaction,
        ];
    }

    $respData = json_decode($response, true);
    if ($httpCode == 200 && isset($respData['status']) && $respData['status']) {
        return [
            'status'  => 'success',
            'rawdata' => $respData,
            'transid' => $transaction . '-refund',
            'fees'    => 0,
        ];
    }

    return [
        'status'  => 'error',
        'rawdata' => $response,
        'transid' => $transaction,
    ];
}