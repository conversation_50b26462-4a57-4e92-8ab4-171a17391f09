<?php
/**
 * Lahza Payment Gateway Callback Handler
 *
 * @package    WHMCS
 * <AUTHOR> Company
 * @copyright  Copyright (c) 2025 Your Company
 * @license    https://www.yourcompany.com/terms
 * @version    1.1.0
 */

// Define WHMCS path constant if not defined
if (!defined('WHMCS_PATH')) {
    define('WHMCS_PATH', dirname(dirname(dirname(__DIR__))) . '/');
}

// Bootstrap WHMCS environment when accessed directly (browser redirect)
if (!defined('WHMCS')) {
    $initPath = WHMCS_PATH . 'init.php';
    if (file_exists($initPath)) {
        define('CLIENTAREA', true);
        require_once $initPath;
    } else {
        // If WHMCS not found, try alternative path
        $initPath = dirname(dirname(dirname(dirname(__FILE__)))) . '/init.php';
        if (file_exists($initPath)) {
            define('CLIENTAREA', true);
            require_once $initPath;
        } else {
            die('WHMCS initialization failed: init.php not found');
        }
    }
}

// Ensure logs directory exists with proper permissions
define('LAHZA_LOG_DIR', dirname(dirname(dirname(__DIR__))) . '/modules/gateways/logs');
if (!file_exists(LAHZA_LOG_DIR)) {
    if (!@mkdir(LAHZA_LOG_DIR, 0755, true)) {
        die(json_encode([
            'status' => 'error',
            'message' => 'Failed to create log directory: ' . LAHZA_LOG_DIR
        ]));
    }
    // Create .htaccess to protect log files
    @file_put_contents(LAHZA_LOG_DIR . '/.htaccess', "Order deny,allow\nDeny from all\n");
    @file_put_contents(LAHZA_LOG_DIR . '/.htpasswd', ''); // Empty file, just for protection
}

/**
 * Log callback activity with error handling and rotation
 */
function logCallback($message, $data = []) {
    $logFile = LAHZA_LOG_DIR . '/lahza_callback.log';
    
    // Log rotation (keep last 10 files, max 5MB each)
    if (file_exists($logFile) && filesize($logFile) > 5 * 1024 * 1024) {
        for ($i = 9; $i >= 1; $i--) {
            if (file_exists("$logFile.$i")) {
                if ($i === 9) {
                    @unlink("$logFile.$i");
                } else {
                    @rename("$logFile.$i", "$logFile." . ($i + 1));
                }
            }
        }
        @rename($logFile, "$logFile.1");
    }
    
    // Sanitize sensitive data before logging
    if (isset($data['data']['authorization'])) {
        $data['data']['authorization'] = '***REDACTED***';
    }
    if (isset($data['data']['customer'])) {
        $data['data']['customer'] = '***REDACTED***';
    }
    
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
        'uri' => $_SERVER['REQUEST_URI'] ?? '',
        'message' => $message,
        'data' => $data
    ];
    
    $logLine = json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . ",\n";
    
    // Write to log file with error suppression
    $result = @file_put_contents($logFile, $logLine, FILE_APPEND);
    
    // If file write fails, try one more time after a small delay
    if ($result === false) {
        usleep(100000); // 100ms delay
        $result = @file_put_contents($logFile, $logLine, FILE_APPEND);
        
        if ($result === false) {
            // Last resort - log to PHP error log
            error_log('Lahza Gateway - Failed to write to log file: ' . $logFile);
        }
    }
}

// Load WHMCS configuration with fallbacks
function loadWhmcsConfig() {
    $possibleConfigPaths = [
        WHMCS_PATH . 'configuration.php',
        dirname(dirname(dirname(__DIR__))) . '/configuration.php',
        dirname(dirname(dirname(dirname(__FILE__)))) . '/configuration.php'
    ];
    
    foreach ($possibleConfigPaths as $configPath) {
        if (file_exists($configPath)) {
            $config = [];
            include $configPath;
            
            // Validate required config values
            $required = ['db_host', 'db_username', 'db_password', 'db_name'];
            $valid = true;
            foreach ($required as $key) {
                if (empty($config[$key])) {
                    $valid = false;
                    break;
                }
            }
            
            if ($valid) {
                return $config;
            }
        }
    }
    
    throw new Exception('Could not load valid WHMCS configuration');
}

// Initialize database connection with retry logic
function initDatabase($config) {
    $maxRetries = 3;
    $retryDelay = 1; // seconds
    
    for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
        try {
            $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 5
            ];
            
            $db = new PDO($dsn, $config['db_username'], $config['db_password'], $options);
            
            // Test the connection
            $db->query('SELECT 1');
            
            return $db;
            
        } catch (PDOException $e) {
            if ($attempt === $maxRetries) {
                throw new Exception("Database connection failed after $maxRetries attempts: " . $e->getMessage());
            }
            
            logCallback("Database connection attempt $attempt failed, retrying...", [
                'error' => $e->getMessage(),
                'next_attempt_in' => "${retryDelay}s"
            ]);
            
            sleep($retryDelay);
            $retryDelay = min(10, $retryDelay * 2); // Exponential backoff with max 10s
        }
    }
    
    throw new Exception('Failed to initialize database connection');
}

try {
    // Load configuration
    $whmcsConfig = loadWhmcsConfig();
    
    // Initialize database connection
    $db = initDatabase($whmcsConfig);
    
    // Set timezone if not set
    if (empty($whmcsConfig['date_timezone'])) {
        date_default_timezone_set('UTC');
    } else {
        date_default_timezone_set($whmcsConfig['date_timezone']);
    }
    
} catch (Exception $e) {
    logCallback('Initialization error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    // Log admin notification attempt
    logCallback('Skipping admin notification - function not available', [
        'error' => $e->getMessage(),
        'suggestion' => 'Enable WHMCS admin notifications in configuration'
    ]);
    
    http_response_code(500);
    die('System error - Please try again or contact support');
}

/**
 * Verify callback signature
 * @param array $data Callback data
 * @param string $signature Received signature
 * @param string $secretKey Your secret key
 * @return bool
 */
function verifyCallbackSignature($data, $signature, $secretKey) {
    $expected = hash_hmac('sha256', json_encode($data), $secretKey);
    return hash_equals($expected, $signature);
}

/**
 * Verify transaction with Lahza API
 * @param string $reference Transaction reference
 * @param string $secretKey Your secret key
 * @return array Transaction data
 * @throws Exception If verification fails
 */
function verifyTransaction($reference, $secretKey) {
    // Get gateway settings to check test mode
    $stmt = $GLOBALS['db']->prepare("SELECT setting, value FROM tblpaymentgateways WHERE gateway = 'lahza'");
    $stmt->execute();
    $gatewayParams = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $gatewayParams[$row['setting']] = $row['value'];
    }
    
    // Lahza uses a single base URL; secret key determines environment
    $baseUrl = 'https://api.lahza.io';
    $url = $baseUrl . "/transaction/verify/" . urlencode($reference);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $secretKey,
            'Content-Type: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 30
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('Failed to verify transaction: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    $result = json_decode($response, true);
    
    if ($httpCode !== 200 || !isset($result['status']) || !$result['status']) {
        $errorMsg = $result['message'] ?? 'Failed to verify transaction';
        throw new Exception('Transaction verification failed: ' . $errorMsg);
    }
    
    return $result['data'] ?? [];
}

// ----------------------------------------------
// Process callback (handles both server-to-server POST webhook and browser GET redirect)
// ----------------------------------------------

// Handle browser redirect (user returning from Lahza checkout)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['reference'])) {
    $reference = trim($_GET['reference']);
    $sessionId = session_id() ?: uniqid('lahza_', true);
    
    try {
        logCallback('Processing browser redirect', [
            'reference' => $reference,
            'query_params' => $_GET,
            'session_id' => $sessionId
        ]);
        
        // Validate reference format
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $reference)) {
            throw new Exception('Invalid reference format');
        }
        
        // Load gateway settings with cache
        static $gatewayParams = null;
        if ($gatewayParams === null) {
            $stmt = $db->prepare("SELECT setting, value FROM tblpaymentgateways WHERE gateway = 'lahza'");
            $stmt->execute();
            $gatewayParams = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $gatewayParams[$row['setting']] = $row['value'];
            }
        }
        
        if (empty($gatewayParams['secretKey'])) {
            throw new Exception('Payment gateway not properly configured - missing secret key');
        }
        
        // Verify transaction via API with retry logic
        $maxRetries = 3;
        $lastError = null;
        
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $transaction = verifyTransaction($reference, $gatewayParams['secretKey']);
                $lastError = null;
                break;
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                if ($attempt < $maxRetries) {
                    usleep(500000 * $attempt); // 0.5s, 1s, 1.5s backoff
                }
            }
        }
        
        if ($lastError) {
            throw new Exception('Transaction verification failed: ' . $lastError);
        }
        
        // Process transaction data
        $invoiceId = (int)($transaction['metadata']['invoice_id'] ?? 0);
        if ($invoiceId <= 0) {
            throw new Exception('Invalid or missing invoice reference in transaction');
        }
        
        $status = strtolower($transaction['status'] ?? '');
        $amount = isset($transaction['amount']) ? ((float)$transaction['amount'] / 100) : 0;
        $transactionId = $transaction['id'] ?? '';
        $currency = $transaction['currency'] ?? '';
        
        // Log transaction details (without sensitive data)
        $logTransaction = $transaction;
        unset($logTransaction['authorization'], $logTransaction['customer']);
        logCallback('Transaction details', [
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'amount' => $amount,
            'currency' => $currency,
            'transaction' => $logTransaction
        ]);
        
        // Process successful payment
        if ($status === 'success') {
            // Check if payment already recorded (idempotent)
            $stmt = $db->prepare("SELECT id, transid, amount FROM tblaccounts WHERE transid = :transid LIMIT 1");
            $stmt->execute([':transid' => $transactionId]);
            $existingPayment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$existingPayment) {
                // Record payment using WHMCS API
                $paymentSuccess = addInvoicePayment(
                    $invoiceId,    // Invoice ID
                    $transactionId, // Transaction ID
                    $amount,        // Amount paid
                    0,              // No fee
                    'lahza'         // Gateway name
                    // Note: Removed $currency parameter as it's not supported in WHMCS < 8.0
                );
                
                if (!$paymentSuccess) {
                    throw new Exception('Failed to record payment in WHMCS');
                }
                
                logCallback('Payment recorded successfully', [
                    'invoice_id' => $invoiceId,
                    'transaction_id' => $transactionId,
                    'amount' => $amount,
                    'currency' => $currency
                ]);
            } else {
                logCallback('Payment already recorded', [
                    'invoice_id' => $invoiceId,
                    'existing_payment' => $existingPayment
                ]);
            }
        }
        
        // Build redirect URL
        $systemUrl = rtrim($whmcsConfig['SystemURL'] ?? $whmcsConfig['systemurl'] ?? '', '/');
        $redirectUrl = $systemUrl . '/viewinvoice.php?id=' . $invoiceId;
        
        // Add status parameter
        $redirectUrl .= (strpos($redirectUrl, '?') === false ? '?' : '&') . 
                       ($status === 'success' ? 'paymentsuccess=true' : 'paymentfailed=true');
        
        // Add reference for tracking
        $redirectUrl .= '&txnid=' . urlencode($transactionId);
        
        logCallback('Redirecting user', ['redirect_url' => $redirectUrl]);
        
        // Ensure no output before header
        if (headers_sent($file, $line)) {
            throw new Exception("Headers already sent in $file on line $line - cannot redirect");
        }
        
        // Redirect with 303 See Other to prevent form resubmission
        header('HTTP/1.1 303 See Other');
        header('Location: ' . $redirectUrl);
        exit;
        
    } catch (Exception $e) {
        $errorData = [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'reference' => $reference,
            'session_id' => $sessionId,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'http_referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ];
        
        logCallback('Redirect processing error', $errorData);
        
        // Try to show user-friendly error page
        $systemUrl = rtrim($whmcsConfig['SystemURL'] ?? $whmcsConfig['systemurl'] ?? '', '/');
        $errorMessage = urlencode('Payment processing error: ' . $e->getMessage());
        
        if (!headers_sent()) {
            header('Location: ' . $systemUrl . '/clientarea.php?action=invoices&paymentfailed=true&message=' . $errorMessage);
            exit;
        }
        
        // Fallback if headers already sent
        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>Payment Processing Error</title>
            <meta http-equiv="refresh" content="5;url=' . $systemUrl . '/clientarea.php?action=invoices">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
                .container { max-width: 800px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
                h1 { color: #d9534f; }
                .error { background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
                .info { margin: 20px 0; padding: 10px; background-color: #e7f3fe; border-left: 5px solid #2196F3; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Payment Processing Error</h1>
                <div class="error">' . htmlspecialchars($e->getMessage()) . '</div>
                <div class="info">
                    <p>We encountered an issue processing your payment. Please contact our support team with the following reference:</p>
                    <p><strong>Reference:</strong> ' . htmlspecialchars($reference) . '</p>
                </div>
                <p>You will be redirected to your invoices page shortly, or <a href="' . $systemUrl . '/clientarea.php?action=invoices">click here</a> to go there now.</p>
            </div>
        </body>
        </html>';
        exit;
    }
}

// Process server-to-server webhook callback (POST request)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payload = file_get_contents('php://input');
    $data = json_decode($payload, true);
    $headers = array_change_key_case(getallheaders(), CASE_LOWER);
    $requestId = uniqid('webhook_', true);
    
    try {
        // Log incoming webhook with request ID for tracking
        logCallback('Webhook received', [
            'request_id' => $requestId,
            'method' => $_SERVER['REQUEST_METHOD'],
            'content_type' => $_SERVER['CONTENT_TYPE'] ?? '',
            'headers' => array_filter($headers, function($key) {
                // Filter out sensitive headers
                return !in_array(strtolower($key), ['authorization', 'x-api-key']);
            }, ARRAY_FILTER_USE_KEY),
            'payload' => $data,
            'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'http_x_forwarded_for' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? null,
            'http_user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        // Validate content type
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (stripos($contentType, 'application/json') === false) {
            throw new Exception('Invalid content type. Expected application/json');
        }
        
        // Validate payload
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON payload: ' . json_last_error_msg());
        }
        
        // Get gateway settings with cache
        static $gatewayParams = null;
        if ($gatewayParams === null) {
            $stmt = $db->prepare("SELECT setting, value FROM tblpaymentgateways WHERE gateway = 'lahza'");
            $stmt->execute();
            $gatewayParams = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $gatewayParams[$row['setting']] = $row['value'];
            }
        }
        
        if (empty($gatewayParams['secretKey'])) {
            throw new Exception('Gateway not properly configured - missing secret key');
        }
        
        $secretKey = $gatewayParams['secretKey'];
        
        // Verify signature
        $signatureHeader = $headers['x-lahza-signature'] ?? '';
        if (empty($signatureHeader)) {
            throw new Exception('Missing required X-Lahza-Signature header');
        }
        
        if (!verifyCallbackSignature($data, $signatureHeader, $secretKey)) {
            throw new Exception('Invalid callback signature');
        }

    // Get reference from callback data
    if (empty($data['reference'])) {
        throw new Exception('Missing transaction reference in callback');
    }
    
        // Verify transaction with Lahza API with retry logic
        $maxRetries = 3;
        $lastError = null;
        
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $transaction = verifyTransaction($data['reference'], $secretKey);
                $lastError = null;
                break;
            } catch (Exception $e) {
                $lastError = $e->getMessage();
                if ($attempt < $maxRetries) {
                    usleep(500000 * $attempt); // 0.5s, 1s, 1.5s backoff
                }
            }
        }
        
        if ($lastError) {
            throw new Exception('Transaction verification failed: ' . $lastError);
        }
        
        // Map transaction data to expected format
        $transactionId = $transaction['id'] ?? '';
        $invoiceId = isset($transaction['metadata']['invoice_id']) ? (int)$transaction['metadata']['invoice_id'] : 0;
        $amount = isset($transaction['amount']) ? ((float)$transaction['amount'] / 100) : 0; // Convert from smallest currency unit
        $status = strtolower($transaction['status'] ?? '');
        $currency = $transaction['currency'] ?? '';
        
        // Log verification result (without sensitive data)
        $logTransaction = $transaction;
        unset($logTransaction['authorization'], $logTransaction['customer']);
        logCallback('Transaction verified', [
            'request_id' => $requestId,
            'transaction_id' => $transactionId,
            'invoice_id' => $invoiceId,
            'amount' => $amount,
            'currency' => $currency,
            'status' => $status,
            'transaction' => $logTransaction
        ]);
        
        // Validate required fields after verification
        if (empty($transactionId) || empty($invoiceId) || $amount <= 0 || empty($currency)) {
            throw new Exception('Invalid transaction data received from Lahza');
        }

        // Get invoice details with proper locking to prevent race conditions
        // Begin transaction for invoice processing
        $db->beginTransaction();
        
        try {
            // Use FOR UPDATE to lock the invoice row
            $stmt = $db->prepare("SELECT id, total, status, userid, paymentmethod
                                 FROM tblinvoices
                                 WHERE id = :invoiceId
                                 LIMIT 1 FOR UPDATE");
            $stmt->execute([':invoiceId' => $invoiceId]);
            $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$invoice) {
                throw new Exception("Invoice not found: {$invoiceId}");
            }
            
            // Check payment method
            if (strtolower($invoice['paymentmethod']) !== 'lahza') {
                throw new Exception(sprintf(
                    'Invoice %s is not set to use Lahza (current: %s)',
                    $invoiceId,
                    $invoice['paymentmethod']
                ));
            }
            
            // Validate amount (with tolerance)
            $amountDiff = abs($amount - (float)$invoice['total']);
            if ($amountDiff > 0.01) {
                throw new Exception(sprintf(
                    'Amount mismatch: %s %s != %s %s',
                    number_format($amount, 2), $currency,
                    number_format($invoice['total'], 2), $currency
                ));
            }

            // Process payment status
            switch ($status) {
                case 'success':
                    // Check for existing payment
                    $stmt = $db->prepare("SELECT id, transid, amount
                                         FROM tblaccounts
                                         WHERE invoiceid = :invoiceId
                                         AND transid = :transid
                                         LIMIT 1");
                    $stmt->execute([
                        ':invoiceId' => $invoiceId,
                        ':transid' => $transactionId
                    ]);
                    
                    if ($stmt->fetch()) {
                        logCallback('Payment already recorded', [
                            'request_id' => $requestId,
                            'invoice_id' => $invoiceId,
                            'transaction_id' => $transactionId
                        ]);
                    } else {
                        // Record payment
                        if (!addInvoicePayment($invoiceId, $transactionId, $amount, 0, 'lahza')) {
                            throw new Exception('Failed to record payment');
                        }
                        
                        logTransaction('lahza', [
                            'invoiceid' => $invoiceId,
                            'transid' => $transactionId,
                            'amount' => $amount,
                            'status' => 'Success'
                        ], 'Success');
                        
                        // Update invoice status
                        if (strtolower($invoice['status']) !== 'paid') {
                            $db->prepare("UPDATE tblinvoices SET status = 'Paid' WHERE id = :invoiceId")
                               ->execute([':invoiceId' => $invoiceId]);
                        }
                    }
                    break;
            }
            
            $db->commit();
            
            logCallback('Callback processing completed', [
                'request_id' => $requestId,
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'status' => $status,
                'amount' => $amount,
                'currency' => $currency
            ]);
            
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollBack();
        }
        throw $e;
    }
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}